﻿

using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Web.Common.Exceptions;

namespace Web.Common.Security
{
    public class CaptchaService
    {
        public CaptchaService(string privateKey, string publicKey, IExceptionHandler exceptionHandler)
        {
            _privateKey = privateKey;
            _publicKey = publicKey;
            this.exceptionHandler = exceptionHandler;
        }

        private readonly string HeaderCaptcha = "g-recaptcha-response";
        private readonly string _privateKey;
        private readonly string _publicKey;
        private readonly IExceptionHandler exceptionHandler;

        public async Task<bool> Validate(string captcha)
        {
            var retVal = false;

#if DEBUG
            return true;
#endif

            try
            {
                if (string.IsNullOrEmpty(captcha))
                {
                    return retVal;
                }

                using (WebClient client = new WebClient())
                {
                    var url = "https://www.google.com/recaptcha/api/siteverify";

                    var key = _privateKey;                    
                    System.Collections.Specialized.NameValueCollection reqparm = new System.Collections.Specialized.NameValueCollection();
                    reqparm.Add("secret", key);
                    reqparm.Add("response", captcha);
                    var responsebytes = await client.UploadValuesTaskAsync(url, reqparm);
                    string responsebody = Encoding.UTF8.GetString(responsebytes);
                    dynamic obj = Newtonsoft.Json.JsonConvert.DeserializeObject(responsebody);
                    string rData = "";
                    rData = obj.success ?? "";
                    bool.TryParse(rData, out retVal);

                }
            }
            catch (Exception ex)
            {
                exceptionHandler.HandleException(ex, ErrorType.SiteLogic);
            }

            return retVal;
        }

        public string PublicKey => _publicKey;


    }
}
