﻿using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Web.Common.Exceptions;
using Web.Common.Extensions;

namespace Web.Common.Security
{
    public class ValidateAntiForgeryTokenMiddleware
    {
        private readonly string _message;
        private readonly RequestDelegate _next;
        private readonly IAntiforgery _antiforgery;

        public ValidateAntiForgeryTokenMiddleware(RequestDelegate next, IAntiforgery antiforgery, string message)
        {
            _next = next;
            _antiforgery = antiforgery;
            _message = message;
        }

        public Task Invoke(HttpContext context) => this.InvokeAsync(context); // Stops VS from nagging about async method without ...Async suffix.

        async Task InvokeAsync(HttpContext context)
        {
            var isBadToken = false;

            if (HttpMethods.IsPost(context.Request.Method))
            {
                try
                {
                    await _antiforgery.ValidateRequestAsync(context);

                }
                catch (AntiforgeryValidationException ex)
                {
                    if (ex.Message.Contains("was meant for user"))
                    { }
                    else
                    {
                        isBadToken = true;                       
                    }

                }
            }

            if (!isBadToken)
            {
                await _next(context);
            }
            else
            {
                var msg = _message ?? "token error";
                throw new AntiForgeryException(msg);

                //if (context.Request.IsAjaxRequest())
                //{
                //    var msg = _message ?? "token error";
                //    await context.Response.WriteAsync(Newtonsoft.Json.JsonConvert.SerializeObject(new { error = msg }));
                //}
                //else
                //{
                //    context.Response.StatusCode = 400;
                //}

            }



        }
    }

    public static class ApplicationBuilderExtensions
    {
        public static IApplicationBuilder UseAntiforgeryTokens(this IApplicationBuilder app, string message)
        {
            return app.UseMiddleware<ValidateAntiForgeryTokenMiddleware>(message);
        }

        public static IApplicationBuilder UseAntiforgeryTokens(this IApplicationBuilder app)
        {
            return app.UseMiddleware<ValidateAntiForgeryTokenMiddleware>();
        }
    }


}
