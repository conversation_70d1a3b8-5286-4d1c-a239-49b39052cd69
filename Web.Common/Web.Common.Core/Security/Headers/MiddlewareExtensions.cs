﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.AspNetCore.Builder;

namespace Web.Common.Security.Headers
{
    public static class MiddlewareExtensions
    {
        public static IApplicationBuilder UseSecurityHeadersMiddleware(this IApplicationBuilder app, Action<SecurityHeadersBuilder> builderAction)
        {
            if (app == null)
            {
                throw new ArgumentNullException(nameof(app));
            }

            if (builderAction == null)
            {
                throw new ArgumentNullException(nameof(builderAction));
            }

            var builder = new SecurityHeadersBuilder();
            builderAction.Invoke(builder);
          
            return app.UseMiddleware<SecurityHeadersMiddleware>(builder.Build());
        }
    }
}
