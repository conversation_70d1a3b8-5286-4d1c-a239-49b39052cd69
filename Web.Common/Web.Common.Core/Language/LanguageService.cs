﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Language
{
    
    public class LanguageService
    {
        //public LanguageService(ILanguagesDataSource languagesDataSource)
        //{
        //    _languagesDataSource = languagesDataSource;
        //}
        //private readonly ILanguagesDataSource _languagesDataSource;

        public string LanguageCookieName => "language";

        public bool IsCorrectLanguage(string lang)
        {
            var _lang = lang.Trim().ToLowerInvariant();

            return true;

            //return _languagesDataSource.Languages().Contains(_lang);            
        }

        public string GetCorrectLanguage(string possibleLanguage)
        {
            return "en";

            //if (string.IsNullOrEmpty(possibleLanguage))
            //{
            //    return _languagesDataSource.DefaultLanguage;
            //}

            //var _lang = possibleLanguage.Trim().ToLowerInvariant();
            //if (!IsCorrectLanguage(_lang))
            //{
            //    return _languagesDataSource.DefaultLanguage;
            //}
            //else
            //{
            //    return _lang;
            //}
           

        }
    }
}
