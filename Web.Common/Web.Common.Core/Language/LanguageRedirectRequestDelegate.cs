﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Web.Common.Exceptions;
using Web.Common.Language;

namespace Web.Common.Language
{
    public class LanguageRedirectRequestDelegate
    {
        public LanguageRedirectRequestDelegate(LanguageService languageService)
        {
            _languageService = languageService;
        }
        private readonly LanguageService _languageService;


        public Task Handle(HttpContext context)
        {         
            var _urlContainsLang = urlContainsLang(context);
            if (_urlContainsLang)
            {
                //How your get?!!! You shouldn't be here
                throw new StatusCodeException(System.Net.HttpStatusCode.NotFound);
            }

            var goodLang = "";
            var _fromCookie = langFromCookie(context);
            if (_fromCookie == null)
            {
                var _fromHeaders = langFromHeaders(context);
                if (_fromHeaders != null)
                {
                    goodLang = _fromHeaders;
                }
            }
            else
            {
                goodLang = _fromCookie;
            }


            if (string.IsNullOrEmpty(goodLang))
            {
                goodLang = _languageService.GetCorrectLanguage("");
            }

            string rawurl = context.Request.Path;
            var retval = "/" + goodLang + rawurl;

            context.Response.Redirect(retval);
            return Task.CompletedTask;
        }


        private string langFromCookie(HttpContext context)
        {
            if (context.Request.Cookies.TryGetValue(_languageService.LanguageCookieName, out string cookieLang))
            {
                if (_languageService.IsCorrectLanguage(cookieLang))
                {
                    return cookieLang;
                }
            }
            return null;
        }

        private string langFromHeaders(HttpContext context)
        {
            var userLangs = context.Request?.Headers["Accept-Language"].ToString() ?? "";
            if (!string.IsNullOrEmpty(userLangs))
            {
                var firstLang = userLangs.Split(',').FirstOrDefault();
                if (!string.IsNullOrEmpty(firstLang))
                {
                    var acceptlang = firstLang.Split('-').FirstOrDefault();
                    return _languageService.GetCorrectLanguage(acceptlang);
                }
            }
            return null;
        }

        private bool urlContainsLang(HttpContext context)
        {
            if (context.Request.Path.HasValue)
            {
                var posLang = context.Request.Path.Value.Split('/', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault();
                if (!string.IsNullOrEmpty(posLang))
                {
                    if (posLang.Length == 2)
                    {
                        return true;
                    }
                }

            }
            return false;
        }

       
    }
}
