﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading.Tasks;
using Web.Common.Exceptions;

namespace Web.Common.Language
{
    public class CultureMiddleware
    {
        private readonly RequestDelegate _next;

        public CultureMiddleware(RequestDelegate next)
        {
            this._next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            if (context.Request.Path.HasValue)
            {
                var mas = context.Request.Path.Value.Split('/', StringSplitOptions.RemoveEmptyEntries);
                if (mas.Length > 0)
                {
                    if (mas[0].Length == 2)
                    {
                        var lang = mas[0];
                        var _languageService = context.RequestServices.GetService(typeof(LanguageService)) as LanguageService;
                        if (!_languageService.IsCorrectLanguage(lang.ToString()))
                        {
                            throw new StatusCodeException(System.Net.HttpStatusCode.NotFound);
                        }

                        try
                        {
                            CultureInfo.CurrentCulture = CultureInfo.CreateSpecificCulture(lang);
                            CultureInfo.CurrentUICulture = CultureInfo.CreateSpecificCulture(lang);
                            context.Response.Cookies.Append(_languageService.LanguageCookieName, lang, new Microsoft.AspNetCore.Http.CookieOptions
                            {
                                HttpOnly = true,
                                Expires = DateTime.Now.AddYears(1),
                                Secure = context.Request.IsHttps,
                            });

                        }
                        catch (CultureNotFoundException) { }

                    }
                }

            }
         
            await _next.Invoke(context);
        }
    }

    public static class CultureExtensions
    {
        public static IApplicationBuilder UseCulture(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<CultureMiddleware>();
        }
    }
}
