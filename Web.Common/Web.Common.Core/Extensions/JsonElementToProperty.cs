﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;

namespace Web.Common.Extensions
{
    public static class JsonElementToProperty
    {
        static private Newtonsoft.Json.JsonSerializer jsonSerializer = new Newtonsoft.Json.JsonSerializer {
            NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore
        };
            

        public static T GetProperty<T>(this JsonElement element, string name)
        {
            var json = JObject.Parse(element.ToString());
            var prop = json.Property(name);
            if (prop == null)
            {
                return default(T);
            }
            var retval = json.Property(name).Value.ToObject<T>(jsonSerializer);
            return retval;
        }
    }
}
