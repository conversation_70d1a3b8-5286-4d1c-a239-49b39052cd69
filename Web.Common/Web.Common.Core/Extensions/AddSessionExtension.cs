﻿namespace Web.Common.Extensions
{
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.DataProtection;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Session;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Redis;
    using StackExchange.Redis;
    using System;
    using System.Collections.Generic;
    using System.Linq;


    public static class AddSessionExtension
    {



        public static IServiceCollection AddSessionRedis(this IServiceCollection services, IConfiguration configuration, string redisLocalConnectionSetting)
        {
            services.AddSingleton<IStartSession, InitSessionMock>();
            services.AddSingleton<ISessionStore, DistributedSessionStoreWithStart>();
            services.AddSession();


            //services.AddStackExchangeRedisCache(option =>
            //{
            //    option.Configuration = configuration.GetConnectionString(redisLocalConnectionSetting);
            //    option.ConfigurationOptions = new StackExchange.Redis.ConfigurationOptions
            //    {

            //        DefaultDatabase = 1
            //    };
            //    option.InstanceName = "Session:";
            //});

            services.AddSingleton<Microsoft.Extensions.Caching.Distributed.IDistributedCache, RedisCache>(service =>
            {
                //if we have registed wrapper then using this one
                //var redisEndpointsService = service.GetService<RedisEndpointsService>();

                var connectionString = configuration.GetConnectionString(redisLocalConnectionSetting);
                var options = ConfigurationOptions.Parse(connectionString);

                string redisTlsCert = configuration.GetValue<string>("redis-tls-cert");
                string redisTlsKey = configuration.GetValue<string>("redis-tls-key");

                options.CertificateSelection += delegate {
                    return new System.Security.Cryptography.X509Certificates.X509Certificate2(redisTlsCert, redisTlsKey);
                };

                options.DefaultDatabase = 1;
                options.ReconnectRetryPolicy = new ExponentialRetry(500, 1000);
                options.SyncTimeout = 3500;

                var cache = new RedisCache(new RedisCacheOptions
                {                    
                    ConfigurationOptions = options,
                    InstanceName = "Session:"
                });
                return cache;
            });

            return services;
        }


        public static IApplicationBuilder ConfigureSession(this IApplicationBuilder app, TimeSpan idleTimeout, string name, string domain = "")
        {
            app.UseSession(new SessionOptions
            {
                IdleTimeout = idleTimeout,
                Cookie = new CookieBuilder
                {
                    IsEssential = true,
                    SecurePolicy = CookieSecurePolicy.SameAsRequest,
                    Name = name,
                    HttpOnly = true,
                    SameSite = SameSiteMode.Lax,
                    Domain = domain
                }
            });
            return app;

        }
    }
}
