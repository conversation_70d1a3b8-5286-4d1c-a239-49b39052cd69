﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using Web.Common.Exceptions;

namespace Web.Common.Extensions
{
    public static class ExtensionsIP
    {
        private static IConfiguration _configuration = DependencyResolver.Current.GetService<IConfiguration>();
        private static IExceptionHandler _exceptionHandler = DependencyResolver.Current.GetService<IExceptionHandler>();

        public static string IP_Real(this HttpContext context, bool logException=true)
        {            
            var retval = "";

            var remoteIpAddressHeader = _configuration.GetValue<string>("RemoteIpAddressHeader");
            if (!string.IsNullOrEmpty(remoteIpAddressHeader))
            {
                retval = context.Request.Headers[remoteIpAddressHeader];
            }
            if (string.IsNullOrEmpty(retval))
            {
                var ipFromProtector = _ipProtector(context.Request.Headers);
                if (string.IsNullOrEmpty(ipFromProtector))
                {
                    retval = context.Connection.RemoteIpAddress.ToString();
                }
                else
                {
                    var mas = ipFromProtector.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    if (mas.Length > 1)
                    {
                        retval = mas[mas.Length - 1];
                    }
                    else
                    {
                        retval = ipFromProtector;
                    }
                }
            }

            retval = retval.Trim();

            if (retval == "::1")
            {
                retval = "127.0.0.1";
            }
            if (retval == "::ffff:127.0.0.1")
            {
                retval = "127.0.0.1";
            }
            
            try
            {                
                var _ipLocal = System.Net.IPAddress.Parse(retval);
                retval = _ipLocal.MapToIPv4().ToString().Trim();                
            }
            catch (Exception ex)
            {
                if (logException)
                {
                    var msg = "Wrong IP: ";
                    if (retval == null)
                    {
                        msg = msg + "NULL";
                    }
                    else
                    {
                        msg = msg + retval;
                    }
                    var _ex = new Exception(msg, ex);
                    _exceptionHandler.HandleException(_ex, ErrorType.SiteLogic);
                }
            }

            

            return retval;

        }


        public static bool IsLocalhost(this HttpContext context)
        {
            var ip = context.IP_Real();
            return ip == "127.0.0.1";
        }


        static List<string> headers = new List<string> {
                "cf-connecting-ip",
                "x-forwarded-for",
                "x-real-ip"
            };
        private static string _ipProtector(IHeaderDictionary headersRequest)
        {
            foreach (var header in headers)
            {
                var ipFromProtector = headersRequest[header];
                if (!string.IsNullOrEmpty(ipFromProtector))
                {
                    return ipFromProtector;
                }
            }
            return null;
        }






    }
}
