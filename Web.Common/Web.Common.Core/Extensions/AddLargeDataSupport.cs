﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Extensions
{
    public static class AddLargeDataSupportExtension
    {
        public static void AddLargeDataSupport(this IServiceCollection services)
        {
            services.Configure<FormOptions>(
          options =>
          {
              options.BufferBodyLengthLimit = int.MaxValue;
              options.MultipartBodyLengthLimit = int.MaxValue;
              options.ValueLengthLimit = int.MaxValue;
              options.MultipartHeadersLengthLimit = int.MaxValue;
          });
            services.Configure<IISServerOptions>(options =>
            {
                options.MaxRequestBodySize = int.MaxValue;
            });

            services.Configure<KestrelServerOptions>(options =>
            {
                options.Limits.MaxRequestBodySize = int.MaxValue;
            });
        }
    }
}
