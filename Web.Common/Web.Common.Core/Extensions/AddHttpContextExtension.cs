﻿namespace Web.Common.Extensions
{    
    using Microsoft.Extensions.DependencyInjection;    
    public static class AddHttpContextExtension
    {
        public static IServiceCollection AddHttpContext(this IServiceCollection services)
        {
            services.AddSingleton<Microsoft.AspNetCore.Http.IHttpContextAccessor, Microsoft.AspNetCore.Http.HttpContextAccessor>();
            return services;
        }
    }
}
