﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Extensions
{
    /// <summary>
    /// http://devtalk.net/2010/09/12/chained-null-checks-and-the-maybe-monad/
    /// </summary>
    public static class MaybeMonadExtensions
    {
        public static TResult Return<TInput, TResult>(this TInput o, Func<TInput, TResult> evaluator, TResult failureValue = default(TResult))
        {
            if (o == null) return failureValue;
            return evaluator(o);
        }

        public static TInput If<TInput>(this TInput o, Func<TInput, bool> evaluator)
            where TInput : class
        {
            if (o == null) return null;
            return evaluator(o) ? o : null;
        }

        public static TInput Unless<TInput>(this TInput o, Func<TInput, bool> evaluator)
            where TInput : class
        {
            if (o == null) return null;
            return evaluator(o) ? null : o;
        }

        public static TInput Do<TInput>(this TInput o, Action<TInput> action)
            where TInput : class
        {
            if (o == null) return null;
            action(o);
            return o;
        }


    //    public static IEnumerable<TSource> DistinctBy<TSource, TKey>
    //(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector)
    //    {
    //        HashSet<TKey> seenKeys = new HashSet<TKey>();
    //        foreach (TSource element in source)
    //        {
    //            if (seenKeys.Add(keySelector(element)))
    //            {
    //                yield return element;
    //            }
    //        }
    //    }
    }
}
