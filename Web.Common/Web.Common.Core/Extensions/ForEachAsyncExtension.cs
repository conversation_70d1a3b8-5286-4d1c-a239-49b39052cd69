﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Web.Common.Extensions
{
    public static class ForEachAsyncExtension    
    {
        public static async Task ExecuteInPartition<T>(IEnumerator<T> partition, Func<T, Task> body)
        {
            using (partition)
                while (partition.MoveNext())
                    await body(partition.Current);
        }

        public static Task ForEachAsync<T>(this IEnumerable<T> source, int degreeOfParallelizm, Func<T, Task> body)
        {
            return Task.WhenAll(
                from partition in Partitioner.Create(source).GetPartitions(degreeOfParallelizm)
                select ExecuteInPartition(partition, body));
        }
    }
}
