﻿namespace Web.Common.Extensions
{
    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Web.Common.Redis;
    using Web.Common.Redis.Storage;

    public static class AddRedisExtension
    {

        public static IServiceCollection AddRedisStorages(this IServiceCollection services, IConfiguration configuration, string redisLocalConnectionSetting)
        {
            addRedisEndpoints(services, configuration, redisLocalConnectionSetting);

            var types = System.Reflection.Assembly.GetAssembly(typeof(RedisResourceStorage)).GetTypes().Where(t => t.IsSubclassOf(typeof(RedisResourceStorage))).ToList();
            types.Add(typeof(RedisResourceStorage));

            foreach (var type in types)
            {
                services.AddTransient(type, s =>
                {
                    var endpoints = s.GetService<RedisEndpointsService>();
                    var cache = s.GetService<RedisCacheStore>();
                    var configuration = s.GetService<IConfiguration>();
                    var wrapper = endpoints.LocalWrapper;
                    var store = (RedisResourceStorage)Activator.CreateInstance(type);

                    var httpContextAccessor = s.GetService<IHttpContextAccessor>();

                    store.Init(wrapper, cache, httpContextAccessor, configuration);

                    return store;
                });
            }
            //add RedisPubSubService
            services.AddSingleton(s =>
            {
                var endpoints = s.GetService<RedisEndpointsService>();
                return new RedisPubSubService(endpoints.BackFrontSharedWrapper, endpoints.LocalWrapper);
            });


            services.AddSingleton<RedisCacheStore>();

            return services;
        }

        private static void addRedisEndpoints(IServiceCollection services, IConfiguration configuration, string redisLocalConnectionSetting)
        {

            RedisEndpointsService serv = new RedisEndpointsService();

            string redisTlsCert = configuration.GetValue<string>("redis-tls-cert");
            string redisTlsKey = configuration.GetValue<string>("redis-tls-key");

            //add RedisLocal
            var redisLocal = configuration.GetConnectionString(redisLocalConnectionSetting);
            if (!string.IsNullOrEmpty(redisLocal))
            {
            
                var connectionMultiplexerBackOffice = new Web.Common.Redis.ConnectionMultiplexerCreator(redisLocal, redisTlsCert, redisTlsKey);
                serv.LocalWrapper = new RedisWrapper(connectionMultiplexerBackOffice);
            }

            //add RedisBackFront
            var redisBackFrontShared = configuration.GetConnectionString("RedisBackFrontShared");
            if (!string.IsNullOrEmpty(redisBackFrontShared))
            {
                var connectionMultiplexer = new Web.Common.Redis.ConnectionMultiplexerCreator(redisBackFrontShared, redisTlsCert, redisTlsKey);
                serv.BackFrontSharedWrapper = new RedisWrapper(connectionMultiplexer);
            }

            services.AddSingleton(s =>
            {
                return serv;
            });

            return;
        }








    }
}
