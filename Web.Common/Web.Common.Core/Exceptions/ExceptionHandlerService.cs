﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Exceptions
{
    public class ExceptionHandlerService : IExceptionHandler
    {
        public ExceptionHandlerService(ILoggerFactory loggerFactory)
        {
            _loggerFactory = loggerFactory;
        }
        private readonly ILoggerFactory _loggerFactory;


        public virtual ErrorAfterHandleType HandleException(Exception e, ErrorType errorType)
        {
            if (e == null) { return ErrorAfterHandleType.Usual_exception; }
            //Логирование ошибки
            exceptionLog(e, errorType);
            return ErrorAfterHandleType.Usual_exception;
        }

        void exceptionLog(Exception ex, ErrorType errorType)
        {
            try
            {
                var isFatalError = errorType == ErrorType.UnHandled;
                if (isFatalError)
                {
                    _loggerFactory.CreateLogger("").LogCritical(ex, "");
                }
                else
                {
                    _loggerFactory.CreateLogger("").LogError(ex, "");
                }
            }
            catch (Exception e)
            {

            }
        }









    }
}
