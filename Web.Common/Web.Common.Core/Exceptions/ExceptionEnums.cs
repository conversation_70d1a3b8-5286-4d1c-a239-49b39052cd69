﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Exceptions
{
    public enum ErrorType
    {
        /// <summary>
        /// Самая страшная ошибка, которую не ждали
        /// </summary>
        UnHandled,

        /// <summary>
        /// Ошибка из бизнеса
        /// </summary>
        BusinessLogic,

        /// <summary>
        /// Ошибка при обработке страницы
        /// </summary>
        SiteLogic,

        /// <summary>
        /// Не правильные данные от пользователя(напр. не верная каптча)
        /// </summary>
        UserDefined,
    }


    public enum ErrorAfterHandleType
    {
        User_not_found,
        Ip_change,
        Ip_banned,
        Usual_exception
    }
}
