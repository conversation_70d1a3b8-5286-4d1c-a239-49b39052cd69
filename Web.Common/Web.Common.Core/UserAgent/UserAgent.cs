﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Web.Common.UserAgent;

namespace Web.Common.UserAgent
{
    public class UserAgent
    {
        public UserAgent(string userAgent)
        {
            _userAgent = userAgent;
        }

        public UserAgent(HttpContext context)
        {
            _userAgent = context?.Request.Headers["User-Agent"] ?? "";
        }
        public string CurrentUserAgent()
        {           
            string usAgentStr = $"Browser:{this.Browser.Name}{this.Browser.Major}, OS:{this.OS.Name}{this.OS.Version}";
            return usAgentStr;
        }

        private string _userAgent;

        private ClientBrowser _browser;
        public ClientBrowser Browser
        {
            get
            {
                if (_browser == null)
                {
                    _browser = new ClientBrowser(_userAgent);
                }
                return _browser;
            }
        }

        private ClientOS _os;
        public ClientOS OS
        {
            get
            {
                if (_os == null)
                {
                    _os = new ClientOS(_userAgent);
                }
                return _os;
            }
        }




      

    }
}
