﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Web.Common.Exceptions;

namespace Web.Common.Middleware
{
    public class StatusCodeExceptionHandler
    {
        private readonly RequestDelegate _request;
        private readonly IExceptionHandler _exceptionHandler;

        public StatusCodeExceptionHandler(RequestDelegate pipeline, IExceptionHandler exceptionHandler)
        {
            this._request = pipeline;
            this._exceptionHandler = exceptionHandler;
        }

        public Task Invoke(HttpContext context) => this.InvokeAsync(context); // Stops VS from nagging about async method without ...Async suffix.

        async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await this._request(context);
            }
            catch (StatusCodeException exceptionStatus)
            {
                if ((int)exceptionStatus.StatusCode != 404)
                {
                    _exceptionHandler.HandleException(exceptionStatus, ErrorType.UnHandled);
                }

                context.Response.StatusCode = (int)exceptionStatus.StatusCode;
                context.Response.Headers.Clear();
            }
            catch (AntiForgeryException antiForgeryException)
            {
                context.Response.StatusCode = 405;
                context.Response.Headers.Clear();
            }
            catch (Exception exception)
            {
                _exceptionHandler.HandleException(exception, ErrorType.UnHandled);

                context.Response.StatusCode = 500;
                context.Response.Headers.Clear();
            }
        }
    }
}
