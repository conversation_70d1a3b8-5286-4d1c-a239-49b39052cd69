﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Logger
{
    public class ElasticNlog : BaseLoggerNlog
    {
        public ElasticNlog(IExceptionMessageFormatter exceptionMessageGenerator, NLog.LogFactory loggerFactory)
        {
            _exceptionMessageGenerator = exceptionMessageGenerator;
            _loggerFactory = loggerFactory;

        }

        private readonly IExceptionMessageFormatter _exceptionMessageGenerator;
        private readonly NLog.LogFactory _loggerFactory;

        public override IDisposable BeginScope<TState>(TState state)
        {
            return null;
        }

        public override bool IsEnabled(LogLevel logLevel)
        {
            return true;
        }
        protected override void Write(Exception exception, LogLevel logLevel)
        {
            var message = _exceptionMessageGenerator.Message(exception);
            this.Write(message, logLevel);
        }


        protected override void Write(string message, LogLevel logLevel)
        {
            if (logLevel == LogLevel.Critical)
            {
                _loggerFactory.GetLogger("fatal").Error(message);
                return;
            }
            if (logLevel >= LogLevel.Error)
            {
                _loggerFactory.GetLogger("error").Debug(message);
                return;
            }

#if DEBUG
            _loggerFactory.GetLogger("info").Debug(message);
#endif
        }
    }



    public class ElasticNlogProvider : ILoggerProvider
    {
        private readonly NLog.LogFactory _loggerFactory;
        private readonly IExceptionMessageFormatter _formatter;

        public ElasticNlogProvider(IExceptionMessageFormatter formatter, string configNlog)
        {
            _loggerFactory = NLog.Web.NLogBuilder.ConfigureNLog(configNlog);
            _formatter = formatter;
        }
        public ILogger CreateLogger(string categoryName)
        {
            return new ElasticNlog(_formatter, _loggerFactory);
        }

        public void Dispose()
        {
        }
    }

}
