﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Web.Common.Logger;
using Web.Common.Extensions;
using Web.Common.Exceptions;

namespace Web.Common.Logger
{
    public class ElasticExceptionMessageFormatter : IExceptionMessageFormatter
    {
        public ElasticExceptionMessageFormatter(IHttpContextAccessor httpContextAccessor, IEnumerable<IExceptionDescriptorInjector> injectors = null)
        {
            _exceptionDescriptor = new ExceptionDescriptor(httpContextAccessor,  injectors);
        }

        private readonly ExceptionDescriptor _exceptionDescriptor;

        public string Message(Exception ex)
        {
            var retval = getMessage(ex);
            return retval;            
        }


        string getMessage(Exception ex)
        {            
            var common = _exceptionDescriptor.GetCommonInfo();
            var exInfo = _exceptionDescriptor.GetExceptionInfo(ex);

            System.Text.StringBuilder sb = new System.Text.StringBuilder();
            sb.AppendLine("");
            if (!string.IsNullOrEmpty(exInfo.HttpCode))
            {
                sb.Append(exInfo.HttpCode);
                sb.Append(", ");
            }
            sb.AppendLine(exInfo.Message);
            sb.AppendLine(exInfo.StackTrace);
            if (!string.IsNullOrEmpty(exInfo.InnerExceptionMessage))
            {
                sb.Append(" ");
                sb.Append(exInfo.InnerExceptionMessage);
            }
            if (!string.IsNullOrEmpty(exInfo.InnerExceptionStackTrace))
            {
                sb.Append(" ");
                sb.Append(exInfo.InnerExceptionStackTrace);
            }

            sb.AppendLine("URL: " + common.URL);

            sb.AppendLine("UserID:" + common.UserID);
            sb.AppendLine("ClientID:" + common.ClientID);
            

            var ip = common.IP;
            if (!string.IsNullOrEmpty(common.IP_forward))
            {
                ip += "(via " + common.IP_forward + ")";
            }
            sb.AppendLine("REMOTE_ADDR: " + ip);
            sb.AppendLine("Browser: " + common.Browser);
            sb.AppendLine("DataKeys: " + exInfo.DataKeys);
            sb.AppendLine("Cookies: " + exInfo.Cookies);
            sb.AppendLine("Headers: " + exInfo.Headers);
            sb.AppendLine("FormKeys: " + exInfo.FormKeys);

            sb.AppendLine("-----------------------------------------");

            var message = sb.ToString();
            return message;
        }

       

    }
}
