﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;
using Web.Common.Exceptions;

namespace Web.Common.Logger
{
    public static class NlogExtensions
    {
              
        public static ILoggerFactory AddFile(this ILoggerFactory factory, IExceptionMessageFormatter formatter, string configNlog)
        {            
            factory.AddProvider(new FileLoggerNlogProvider(formatter, configNlog));
            return factory;
        }

        public static ILoggerFactory AddElastic(this ILoggerFactory factory, IExceptionMessageFormatter formatter,string configNlog)
        {
            factory.AddProvider(new ElasticNlogProvider(formatter, configNlog));
            return factory;
        }
    }
}
