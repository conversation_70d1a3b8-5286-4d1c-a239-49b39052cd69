﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using Web.Common.Exceptions;
using Web.Common.Extensions;

namespace Web.Common.Logger
{
    public class ExceptionDescriptor
    {
        public ExceptionDescriptor(IHttpContextAccessor httpContextAccessor, IEnumerable<IExceptionDescriptorInjector> injectors=null)
        {
            _httpContextAccessor = httpContextAccessor;            
            _injectors = injectors;
        }
        private readonly IHttpContextAccessor _httpContextAccessor;        
        private readonly IEnumerable<IExceptionDescriptorInjector> _injectors;


        public class CommonInfo
        {
            public string URL { get; set; }

            public string ClientID { get; set; }

            public string Client_Name { get; set; }

            public string IP { get; set; }
            public string Host { get; set; }

            public string IP_forward { get; set; }
            public string Host_forward { get; set; }

            public string Browser { get; set; }

            public string HttpMethod { get; set; }

            public string SessionID { get; set; }

            public int UserID { get; set; }

            //public string FormKeys { get; set; }
        }

        public class ExceptionInfo
        {
            public string Message { get; set; }
            public string StackTrace { get; set; }

            public string InnerExceptionMessage { get; set; }
            public string InnerExceptionStackTrace { get; set; }


            public string DataKeys { get; set; } = "";
            public string FormKeys { get; set; } = "";
            public string HttpCode { get; set; } = "";
            public string Cookies { get; set; } = "";
            public string Headers { get; set; } = "";

        }


        public CommonInfo GetCommonInfo()
        {
            CommonInfo info = new CommonInfo();

            if (_httpContextAccessor.HttpContext != null)
            {
                info.URL = _httpContextAccessor?.HttpContext?.Request.Path;
            }


            //IP и HostName            
            try
            {
                if (_httpContextAccessor.HttpContext != null)
                {
                    info.IP = _httpContextAccessor?.HttpContext?.IP_Real(false);
                }
            }
            catch (Exception ex)
            {
                //_exceptionHandler.HandleException(ex, ErrorType.SiteLogic);
            }


            if (_httpContextAccessor.HttpContext != null)
            {
                var userAgent = _httpContextAccessor?.HttpContext?.Request.Headers["User-Agent"];
                var ua = new UserAgent.UserAgent(userAgent);
                string browser = ua.Browser.Name;
                browser += " " + ua.Browser.Major;
                info.Browser = browser;
            }

            if (_httpContextAccessor.HttpContext != null)
            {
                var method = _httpContextAccessor?.HttpContext?.Request.Method;
                var isAjax = _httpContextAccessor?.HttpContext?.Request.IsAjaxRequest() ?? false;
                if (isAjax)
                {
                    method += "-AJAX";
                }
                info.HttpMethod = method;
            }

            if (_injectors != null)
            {
                foreach (var item in _injectors)
                {
                    item.Update(info);
                }                
            }

            return info;
        }

        public ExceptionInfo GetExceptionInfo(Exception ex)
        {
            ExceptionInfo info = new ExceptionInfo();
            StatusCodeException httpex = ex as StatusCodeException;
            if (httpex != null)
            {
                info.HttpCode = httpex.StatusCode.ToString();
            }

            info.Message = ex.Message;
            info.StackTrace = ex.StackTrace;


            if (ex.InnerException != null)
            {
                info.InnerExceptionMessage = ex.InnerException.Message;
                info.InnerExceptionStackTrace = ex.InnerException.StackTrace;
            }


            if (_httpContextAccessor.HttpContext != null)
            {
                //var formKeys = _httpContextAccessor?.HttpContext?.Request.Form.ToDictionary(x => x.Key, x => x.Value.ToString());
                //foreach (string el_k in formKeys.Keys)
                //{
                //    string value = "";
                //    if (el_k.Contains("txtPassword"))
                //    {
                //        value = "*******";
                //    }
                //    else if (el_k.Contains("Password"))
                //    {
                //        value = "*******";
                //    }
                //    else
                //    {
                //        value = formKeys[el_k].ToString();
                //    }
                //    info.FormKeys += "POST[" + el_k + "]=\"" + value + "\"";

                //}

                foreach (string el in _httpContextAccessor?.HttpContext?.Request.Cookies.Keys)
                {
                    info.Cookies += "COOKIE[" + el + "]=\"" + _httpContextAccessor?.HttpContext?.Request.Cookies[el] + "\"" + " ";
                }


                foreach (string el in _httpContextAccessor?.HttpContext?.Request.Headers.Keys)
                {
                    info.Headers += "Header[" + el + "]=\"" + _httpContextAccessor?.HttpContext?.Request.Headers[el] + "\"" + " ";
                }

            }
            foreach (string ld in ex.Data.Keys)
            {
                var exData = ex.Data[ld] ?? "";
                info.DataKeys += "ExceptionData[" + ld + "]=\"" + exData.ToString() + "\"";
            }



            return info;

        }
    }

   

   

}
