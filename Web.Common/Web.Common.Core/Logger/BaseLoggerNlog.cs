﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Logger
{
    public abstract class BaseLoggerNlog : ILogger
    {
       

        public abstract IDisposable BeginScope<TState>(TState state);
        public abstract bool IsEnabled(LogLevel logLevel);


        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            List<Exception> exs = new List<Exception>();

            if (exception != null)
            {
                var exAgr = exception as AggregateException;
                if (exAgr != null && exAgr.InnerExceptions != null)
                {
                    exs.AddRange(exAgr.InnerExceptions);
                }
                else
                {
                    exs.Add(exception);
                }

                foreach (var item in exs)
                {
                    Write(item, logLevel);
                }
            }
            else
            {
                Write(state.ToString(), logLevel);
            }
        }
        protected abstract void Write(Exception exception, LogLevel logLevel);
        protected abstract void Write(string message, LogLevel logLevel);


    }




}
