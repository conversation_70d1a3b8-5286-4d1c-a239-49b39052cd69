﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.Logger
{
    public class FileLoggerNlog : BaseLoggerNlog
    {
        public FileLoggerNlog(IExceptionMessageFormatter exceptionMessageGenerator, NLog.LogFactory loggerFactory)
        {
            _exceptionMessageGenerator = exceptionMessageGenerator;
            _loggerFactory = loggerFactory;


        }
        private readonly IExceptionMessageFormatter _exceptionMessageGenerator;
        private readonly NLog.LogFactory _loggerFactory;


        public override IDisposable BeginScope<TState>(TState state)
        {
            return null;
        }

        public override bool IsEnabled(LogLevel logLevel)
        {
            return true;
        }
        protected override void Write(Exception exception, LogLevel logLevel)
        {
            var message = _exceptionMessageGenerator.Message(exception);
            this.Write(message, logLevel);
        }


        protected override void Write(string message, LogLevel logLevel)
        {
            NLog.LogLevel nlogLevel = NLog.LogLevel.Trace;
            switch (logLevel)
            {
                case LogLevel.Trace:
                    nlogLevel = NLog.LogLevel.Trace;
                    break;

                case LogLevel.Debug:
                    nlogLevel = NLog.LogLevel.Debug;
                    break;

                case LogLevel.Information:
                    nlogLevel = NLog.LogLevel.Info;
                    break;

                case LogLevel.Warning:
                    nlogLevel = NLog.LogLevel.Warn;
                    break;
                case LogLevel.Error:
                    nlogLevel = NLog.LogLevel.Error;
                    break;
                case LogLevel.Critical:
                    nlogLevel = NLog.LogLevel.Fatal;
                    break;
                case LogLevel.None:
                    nlogLevel = NLog.LogLevel.Debug;
                    break;
                default:
                    break;
            }

            _loggerFactory.GetCurrentClassLogger().Log(nlogLevel, message);

            //            if (logLevel == LogLevel.Critical)
            //            {
            //                _loggerFactory.GetLogger("fatal").Error(message);
            //                return;
            //            }
            //            if (logLevel >= LogLevel.Error)
            //            {
            //                _loggerFactory.GetLogger("error").Debug(message);
            //                return;
            //            }
            //            else if(logLevel >= LogLevel.Warning)
            //            {
            //                _loggerFactory.GetLogger("warn").Warn(message);                
            //            }

            //#if DEBUG
            //            _loggerFactory.GetLogger("trace").Trace(message);
            //#endif
        }
    }



    public class FileLoggerNlogProvider : ILoggerProvider
    {
        private readonly NLog.LogFactory _loggerFactory;
        private readonly IExceptionMessageFormatter _formatter;
        public FileLoggerNlogProvider(IExceptionMessageFormatter formatter, string configNlog)
        {
            _loggerFactory = NLog.Web.NLogBuilder.ConfigureNLog(configNlog);
            _formatter = formatter;
        }
        public ILogger CreateLogger(string categoryName)
        {
            return new FileLoggerNlog(_formatter, _loggerFactory);
        }

        public void Dispose()
        {
        }
    }

}
