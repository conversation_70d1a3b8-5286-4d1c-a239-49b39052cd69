﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Web.Common.SettingsApplication
{
    public class SettingsGUIDService
    {

        private Dictionary<string, string> _dictGuid = new Dictionary<string, string>();

        public Guid GetSettingGUIDByName(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                throw new Exception("No setting name");
            }
            var lastSettinPart = name.Split('.').ToList().Last();
            _dictGuid = getDict();
            var key = _dictGuid.Keys.FirstOrDefault(x => x.Contains(lastSettinPart));
            if (key == null)
            {
                throw new Exception($"No setting with name: {name}");
            }

            if (Guid.TryParse(_dictGuid[key], out Guid guid))
            {
                return guid;
            }
            else
            {
                throw new Exception($"Can nto parce to guid. Name: {name}. Possible guid: {_dictGuid[key]}");
            }
        }


        private Dictionary<string, string> getDict()
        {
            Dictionary<string, string> retval = new Dictionary<string, string>();


            SettingsConstants obj = new SettingsConstants();
            foreach (var prop in typeof(SettingsConstants).GetFields(BindingFlags.Public | BindingFlags.Static))
            {
                retval[prop.Name] = prop.GetValue(obj).ToString();                
            }

            return retval;
        }


    }
}
