﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.SettingsApplication
{
    public class SettingsConstants
    {

        public const  string BoomwareSMSProvider_BoomwareAPIToken = "6F7F6EB2-0780-42B5-9483-F386E24D7CD6";
        public const  string TwilioProvider_TwilioAccountSID = "7035765D-393E-47B8-8594-5CBFEBC46D59";
        public const  string TwilioProvider_TwilioAuthToken = "D3CCBD65-59CE-47A1-8194-C5F26934FF43";
        public const  string TwilioProvider_TwilioSMSFromNumber = "EBED5F9C-9FEE-4893-A743-322B61CEFF6B";
        public const  string VoiceCallProvider_VoiceCallProviderPassword = "C3105FD5-4D74-40FA-ADE0-2C118F01263D";
        public const  string VoiceCallProvider_VoiceCallProviderURL = "96AA31C6-9D1F-452A-B581-BCC2775DF94B";
        public const  string VoiceCallProvider_VoiceCallProviderUserName = "6386AA6C-7920-4449-AD78-01F4D94476DB";
        public const  string VoiceCallProvider_VoiceCallServiceAuthenticationId = "180D2428-EAD7-4012-BB74-9B4518DEA670";
        public const  string VoiceCallProvider_VoiceCallServiceDomain = "ECA10B4B-5C49-4F79-8DEB-245500E3ACF6";
        public const  string VoiceCallProvider_VoiceCallServiceOzekiLicenseKey = "A628B268-5116-4AEB-A118-E7D486B9CF4D";
        public const  string VoiceCallProvider_VoiceCallServiceOzekiUserId = "91C41919-8290-4931-A59B-E1A8CD57AFD7";
        public const  string VoiceCallProvider_VoiceCallServicePassword = "11BD8931-FD80-4D9B-83C5-1B3FA4189C89";
        public const  string VoiceCallProvider_VoiceCallServiceUserName = "1AB91787-2E44-4341-93AD-970CD1A5D626";
        public const  string MaxRPMForContactsLookup = "105F572F-9BED-4242-968C-8C6099AFBB8F";
        public const  string RecurrentTaskIntervalClearClientAttempts = "4B4BC46C-8E48-4AE5-AC03-37CA66D244E2";
        public const  string TasksServiceThreadsPerCore = "8290A86A-4A76-43DF-B2F1-124D7D958892";
        public const  string RecurrentTaskIntervalCheckPostponedTickets = "D787B5D2-9253-4DA6-8437-38E1301E90E4";
        public const  string SupportTicketPrefix = "78D4C65D-72AF-46DE-A3F1-6875A1E52A14";
        public const  string BackOfficeBackgroundImage = "E994C68C-CAF2-4CF6-A469-1F447596CFC6";
        public const  string BackOfficeColorScheme = "FA78D7BC-0CD4-4FAD-8303-08039D4A0A0F";        
        public const  string BackOfficeLogoOverride = "C5EC0ED7-D688-4A28-9305-715D9FCAF707";
        public const  string BackOfficeLogoRectangleImage = "4EBC74D9-22D4-442E-8C56-019796B34E2C";
        public const  string BackOfficeLogoSquareImage = "DB5B9A73-BCA1-41BE-A02B-A834819FC5A9";
        public const  string BackOfficeSmtpFrom = "B786D8B7-7D51-406A-BE7A-180ECC6C8E36";
        public const  string BackOfficeSmtpHost = "3447662B-6046-445F-A1EE-13E26906D4C4";
        public const  string BackOfficeSmtpIsFrontSMTPUsed = "CB915AB6-C3E1-42A8-B5D4-2A077591C6C9";
        public const  string BackOfficeSmtpPassword = "6EF85FD3-03EF-466E-8972-31AF0A3B1F30";
        public const  string BackOfficeSmtpUser = "3B5E8278-9D48-4EF3-B546-751F43E7AF8B";
        public const  string BackOfficeSmtpUseSSL = "1466E91B-E185-4588-9485-0CC63B52E90E";
        public const  string BuyerDefaultChartOfAccountsID = "7D12AABB-712F-4B99-85DA-8971CD9696FB";        
        public const  string ClientAccountChartOfAccountsID = "E8F4E363-5138-4934-A0B2-FA21C1AF9FAF";
        public const  string ClientBoardingScoreCardID = "1946E9FD-C3E9-456A-B14D-717EE548868E";
        public const  string ClientDocumentsAreStoredInFolder = "BBF9C398-6779-4B88-915F-5099EE563028";
        public const  string ClientDocumentsFolderLocation = "D60C0A3C-9B6A-4BE7-873A-CE69767A2EEE";
        public const  string ClientRegistrationLimitGroupID = "351FA131-9836-4A8C-9FF4-480B3A187485";
        public const  string ClientRegistrationTariffID = "ADFFC60F-0B28-4A6F-B89D-04CD07D58D9E";
        public const  string Company2LetterCode = "349E02C7-C5F1-4F22-934E-4ADB660FB7EA";
        public const  string Company3LetterCode = "C810D9E9-70CA-4479-A5CF-EFCA0C38F29E";
        public const  string CompanyBaseCurrency = "E1B9C463-2ECA-4D88-8FE7-85B30D69E2BB";
        public const  string CompanyLegalName = "2F2DF14F-D89F-4998-AEE2-87727F17A032";
        public const  string CompanyLogo = "876EEF9C-5E65-4786-8D3E-98ACA8969A6E";
        public const  string CompanyShortName = "21952B62-F156-4EEE-9B2B-9E5526090B3F";
        public const  string CompanySystemClientID = "8B525872-D792-4EEC-B066-FE5959F21938";
        public const  string CompanySystemUserID = "E0C832DA-8151-4429-860D-D6D787988C90";
        public const  string DomainApiSetting = "AF6A45E5-8631-445A-82F1-0F41945739EF";
        public const  string DomainBackOfficeSetting = "E08D39A3-5A3C-472D-A550-46248136F60C";
        public const  string DomainCabinetSetting = "48CBDCA8-A252-4A88-A9FB-48B472A2370F";
        public const  string DomainCheckoutSetting = "837F0DD8-ABAA-4199-A8B8-11CD4B6802AD";
        public const  string DomainDevSetting = "C0C2BCF1-2498-4520-8036-BEA4447A79D7";
        public const  string DomainForumSetting = "8256C06F-713C-4DC7-BA96-FA7D53EB7416";
        public const  string DomainGlobalSetting = "84D03C04-114E-49CE-BD43-4AAC6CD61740";
        public const  string DomainMediaSetting = "3CF3DB8B-81BE-4A72-BC30-32E328CA78F5";

        public const  string DomainPublicSetting = "28E44097-E931-4B0E-8D6A-0EF70361C90E";
        public const  string DomainStatusSetting = "D74BBE90-A2C2-4225-904B-7F3A93670FA5";
        public const  string DomainSupportSetting = "0C8C6FF1-D70F-4640-9954-05288B9A7E44";
        public const  string DormancyFeeAccount = "A2B333AC-7F00-4BD5-B349-A3B148FA2646";
        public const  string DormantStatusTriggerData = "242F158A-D7EF-4973-853F-AB5EDB380DE3";
        public const  string External_SSOProvider_GUID = "E7CC9CD6-0178-45DD-95F8-6697A20B96C8";

        public const  string FrontOfficeGoogleCaptchaPrivateKey = "506F3494-22F6-4694-8E83-B3ADEA5AEFD0";
        public const  string FrontOfficeGoogleCaptchaPublicKey = "4263D29D-ED5A-407D-B028-ECE10E0B9955";

        public const string FrontOfficeGoogleAddressAutocompleteKey = "C644E30E-8B1D-4448-B84A-1EBB9D185B4D";

        public const  string FrontOfficeSmtpFrom = "5774BC90-60D9-44C5-85AC-262B8298A5D6";
        public const  string FrontOfficeSmtpHost = "5650EA66-2006-4930-9E5C-B67A366EBC37";
        public const  string FrontOfficeSmtpPassword = "252969E2-EA96-46DF-9FAA-2353462D61C7";
        public const  string FrontOfficeSmtpUser = "743F6E29-D83F-42E4-AD4D-30EC3989C2AE";
        public const  string FrontOfficeSmtpUseSSL = "EC2BF434-D1C6-4F27-864E-12A8BD8C5891";
        public const  string GeneralBankTariffID = "72F79FA3-331B-467D-9ED2-8DE9223B83AA";
        public const  string GeneralPurchasesAccountID = "4B06A46A-339E-4103-8D96-4E7C249CBADB";
        public const  string GeneralSalesAccountID = "BD1C7B01-5568-4C26-9629-B535698CF161";
        public const  string GeneralTariffID = "531337A7-15AF-4068-B236-129A758FD2C3";
        public const  string GlobalServiceName = "2CDF7DBD-5F6C-43E2-965D-B138ED15E69F";
        public const  string HoldOperationOnRiskScoreValue = "F6087100-B39F-4D4E-BB94-673950940DD4";
        public const  string ImportTranslationsPath = "BD48A241-04C5-4B55-9693-CFEA3CF9EA54";
        public const  string IntegrationSSOURL = "F1C023B1-31E5-44C7-B64B-0D9921B0D9BD";
        public const  string IPNCheckoutPrefixSetting = "********-D8FB-4A5B-8FD2-FE6E2B345D59";
        public const  string IsDormancyFeeActive = "E3EC1B51-B516-4D72-8EAB-1B6BC3EFE210";
        public const  string IsModuleExchangeInstalledSetting = "E50A4F18-8360-496F-A4C8-0E0BC93BC64D";
        public const  string LimitsLvl1Corporate = "2693DC52-8256-40DF-9404-456B3F1CA568";
        public const  string LimitsLvl1Personal = "CA21B3E8-E88C-4EA1-84DD-A0B2489305F0";
        public const  string LimitsLvl2Corporate = "E58DE05F-EBAB-4CB5-A567-8D043E177CB0";
        public const  string LimitsLvl2Personal = "56E69B73-053A-45CB-AE87-F11B090D9747";
        
        public const  string MonthsCountToCloseDormantAccount = "607A86BC-4A93-452F-B3F6-A92AEF2C9AEE";
        public const  string OrganizationTariffID = "850536C1-6D4A-42AD-B86D-4BE6B5D468F4";
        public const  string PersonalTariffID = "********-153F-48FB-AA17-C8ABF1C9B20E";
        public const  string RegulaServerURLSetting = "E997803B-0AEB-4556-909D-CC9403F119C5";        
                        
        public const  string SecurityBruteforceLogDepthSeconds = "F30AC0E0-D1B9-444A-92E1-F726A27C8F77";
        public const  string SecurityMaxFailedLoginsBeforeBlock = "21E49C88-815F-42A9-8FFD-8FC0D27CC6E3";
        public const  string SecurityPasswordHistoryCheckPasswordsCount = "67677D5E-7532-465E-BED1-8A17F8FA2228";
        public const  string SecurityPinExpireTimeMinutes = "6F09E49B-9AB9-4C55-8E4B-7CF8AC5AB5CB";
        public const  string SecurityPreviousCountryLookupDisabled = "50B25E49-3787-4F57-AE99-E6B2EC37E2FE";
        public const  string SupplierDefaultChartOfAccountsID = "5B7FAEDD-4052-49FD-A7B5-20F18B951EAB";
        public const  string SystemPaymentOperationPattern = "E50B5762-5890-4FEC-94FD-26089E715C3F";
        public const  string TasksQueueMaxTriesCount = "853285F2-361F-4144-8F53-A27C876E08AF";
        public const  string TasksQueueRetryPause = "6B2E49B3-DFF2-4F6C-9735-826E43321BE9";        
        public const  string VideoVerificationServiceTokboxLogin = "A6E01A16-A85F-4474-88F8-086FAAC513E1";
        public const  string VideoVerificationServiceTokboxPassword = "********-C6FD-467C-B135-5A8235276D07";        
        public const  string ZeroLimitsGroupID = "B7CD6C62-C7EE-49A7-A2BE-BDAA70F5F64C";
        public const  string FixerRatesProviderAPIKey = "DCB602DF-82C3-441B-824D-33D5469141A6";
        public const  string СurrencyСonverterFreeAPIKeySetting = "C99F7042-A745-485B-92CE-CDB01D06822B";
        public const  string DectaFailureURLSetting = "56349BD7-72DF-47C1-8E53-540DFA5F5B1C";
        public const  string DectaSecretKeySetting = "6D2D4EFA-B583-4CBB-B20F-EC3B2555E578";
        public const  string DectaSkipCaptureSetting = "17D98852-8FB5-4885-8962-A7625B29CE5F";
        public const  string DectaSuccessURLSetting = "7F96227F-57D2-48B1-A692-77C99B4CF93B";
        public const  string MyriadPaymentsPluginAuthorizationUrl = "B5FFD950-CA5B-4BCD-8258-52AEAE45E6EE";
        public const  string MyriadPaymentsPluginCancelUrl = "FD438C0B-4185-41E7-BB55-DA8BFC782533";
        public const  string MyriadPaymentsPluginErrorUrl = "47A64C1D-FF50-4A58-8285-730305A02D23";
        public const  string MyriadPaymentsPluginIsTest = "68B5BBB2-E8E6-4B4F-84C2-E747E72D389A";
        public const  string MyriadPaymentsPluginMerchantId = "3AF2C9A7-61B7-48F2-BFB5-5F789BF63F28";
        public const  string MyriadPaymentsPluginPendingUrl = "14395BAC-A5A5-454A-9800-96E386890DF6";
        public const  string MyriadPaymentsPluginProductId = "765E9392-A503-4AD3-BD7B-283DC0F0E635";
        public const  string MyriadPaymentsPluginSessionTokenUrl = "8F8B85E9-F9E5-45CB-875E-305033B6ED15";
        public const  string MyriadPaymentsPluginStatusUrl = "40668FF9-982A-435E-A9AF-CA0B7F56ACC9";
        public const  string MyriadPaymentsPluginSuccessUrl = "C78675F3-929C-458F-B693-526C05FCFECD";
        public const  string MyriadPaymentsPluginWebCashierUrl = "49358E2A-9663-4C08-A3A1-E02C227D6C1F";
        public const  string MaxMindAccountID = "519BD7B7-C6EC-4B3F-A939-A5805270CA7C";
        public const  string MaxMindLicenseKey = "AE66F04E-CC1E-46E1-964A-A3687259098C";
        public const  string Firebase_Icon = "5E543EB6-9EA1-43B7-816B-551B3A916842";
        public const  string Firebase_Sender_ID = "143C2097-54D9-41C1-8B53-F1373A2E025F";
        public const  string Firebase_Server_Key = "3D609776-05E5-427A-A152-76C9F23C6019";
        public const  string Firebase_Title = "D2A9E916-E1DC-4FEA-8256-3CFDA5843F8E";
        public const  string KYCQueueTimeToProcess = "CA9592CD-5C3C-42A1-A570-AE22F017EAFD";
        public const  string KYCValidationDateCustomerMinAge = "65E8331D-615B-4A90-9B5F-3A7949A2D1BD";
        public const  string KYCValidationDocumentNumberStringRegexp = "305E991B-B41E-4501-8B3C-59B82AA80393";
        public const  string KYCValidationEmailStringRegexp = "B7A9A277-88DF-4C42-A19A-7C996EFBB6DF";
        public const  string KYCValidationFileMaxSize = "6EBA7286-1B5E-4697-8AB9-7D4E94601E91";
        public const string KYCValidationFileMaxSizeBackoffice = "3d4fc47a-cbf4-4ec6-8ad5-c0f93f9059ea";
       
        public const  string KYCValidationGeneralStringRegexp = "31467B88-5983-497A-ABD0-30E4FE15E168";
        public const string KYCIsRegexpValidationEnabled = "C7C633FC-47A8-4EB9-B552-ACDCA2849E9D";

        public const  string KYCValidationPhoneStringRegexp = "3061144F-1A45-4EE2-BA3A-125AD1F6A9D5";
        public const  string KYCValidationURLStringRegexp = "FB59EEFE-9709-40A7-AC66-D638AB23C9F0";
        public const  string KYCValidationZipCodeStringRegexp = "CE82B7E1-4C0E-4CAC-BF98-CDBEB913064A";
        public const  string ProductsQueueTimeToProcess = "C38778C4-5009-4078-A94D-02DB17796E19";
        public const  string Notifications_Expire_Days = "EB0E0A99-DCE0-453D-B94B-0D7BE57C1281";
        public const  string RecurrentTaskIntervalNotificationsDelete = "00B601F7-6D76-47B8-BFF1-0B8B6332571F";
        public const  string TokenInternal_Expire_Seconds = "A96E3100-1B88-4668-97E3-73E96917A864";
        public const  string AccountingClosurePeriodDaysSetting = "69BD383C-7F9B-447A-AEC3-6797FA63AC9E";
        public const  string AccountStatementMaxTransactionsSetting = "83BE210B-CB22-4C00-8230-B3E5C4FE8F6D";
        public const  string ChartOfAccountsDelimiterSetting = "A7E22577-5A92-4BE2-AA01-E8D3AF9B26CB";
        public const  string ClientAccountPrefixSetting = "3B162EF3-4859-4BF3-B87F-2AE3924ECB22";
        public const  string ClientCloseSupportCategorySetting = "9DDB25B9-AA56-4E21-94FD-00402ED3BCD1";
        public const  string IsClientAccountDesignationUsedSetting = "9AF4FC53-2B6F-4B8C-8E8A-A88CE4C1443D";
        public const  string PaymentAccountSetting = "0475361C-886A-46C4-8C02-1CE96314F9BA";
        public const  string PdfStatementTemplate = "4ED55363-19C4-4C6F-973A-F53AC02CE31B";
        public const  string PdfStatementTemplateLogo = "42B65529-7439-44E7-958C-8B5D3F18DFC4";
        public const  string AuthorizationBlockTimeInterval = "6BEB1E64-C3B0-487C-8678-AF80D201ADCB";
        public const  string AuthorizationCodeDigitsNumberSetting = "DBA6847A-7838-458B-ACF5-D49FA4541977";
        public const  string AuthorizationPower = "ADF5CB2D-8229-40DB-A142-754150A1134C";
        public const  string AuthorizationSecondBlockTimeInterval = "6060D5E3-9527-4C73-8288-61DC2608465A";
        public const  string AuthorizationSMSInterval = "D357951A-86F1-4584-B650-0E225B0612D3";
        public const  string AuthorizationTimeInterval = "A531180D-3974-4E80-8FC7-376362B7475F";
        public const  string AuthorizationTriesCount = "AFC97A9C-0380-45B5-9958-0FD0E4078667";
        public const  string JournalEntryPatternSetting = "CAD9625A-E7F9-496C-8BE9-A85BEE45B407";
        public const  string ClientCloseMessageTemplateSetting = "A9AE87BD-CB60-4C05-AA7C-90CB5FE055F1";
        public const  string ExchangeRateScaleSetting = "658606FD-BCFC-430F-AA2E-86D791C85077";
        public const  string IsExchangeRateDependsOnAmount = "8DED5CAE-01B2-40F1-B329-799C3F056FE9";
        public const  string IsExchangeRateDependsOnWorkingTime = "84DF463F-5AA9-4B29-985A-70B1776A10F6";
        public const  string IsAccountClosingRequireAuthSetting = "DB7A4C0C-0CF0-4F17-87EB-0B143E221B6D";
        public const  string RecurrentTaskIntervalAccountingClosures = "4F22FA2A-5A1F-4990-9F05-178FB3BF481A";
        public const  string RecurrentTaskIntervalClientExternalDbCheck = "108BDB02-CCBC-4144-9FD4-7A5B70683ED8";
        public const  string RecurrentTaskIntervalDormantTrigger = "252E46C8-F4B1-439A-94E5-DDB6D48B3A35";
        public const  string RecurrentTaskIntervalDropClientVerification = "2F6B7EFF-862E-4E8C-AEFB-D1AD8E7D7D70";
        public const  string RecurrentTaskIntervalPerfomanceCounters = "D7EBE535-8A56-45E2-B542-51119E5A62DE";
        public const  string RecurrentTaskIntervalReferralPayments = "D7BFE326-1FEE-4F11-8876-E6BFA60A93AB";
        public const  string RecurrentTaskIntervalUpdateBookkeppingRates = "4EB3931B-36EC-4983-BE5F-A77CFC16AE3D";
        public const  string RecurrentTaskIntervalUpdateCurrencyRates = "A3280B1F-64CD-4C69-9FF5-09E2B8C62A70";
        public const  string RecurrentTaskIntervalSystemPayments = "2CD8E406-CB8B-482B-8640-B6BE7F64BA96";
        public const  string TransactionColorCanceled = "B665CE3B-51F6-444C-AB25-C56D7B0369F2";
        public const  string TransactionColorCompleted = "37F703BE-16D4-4CF3-8957-3CF1C73084CD";
        public const  string TransactionColorCreated = "67F61E22-22F8-441C-A485-89EDC930F3D3";
        public const  string TransactionColorError = "3F24F631-69E9-4BA3-AF63-EB5F3A423B56";
        public const  string TransactionColorHold = "AF35263C-8AEF-4AAF-AE95-5A8E2A4824E4";
        public const  string TransactionColorPending = "CBF89AAB-C1EF-4662-8DD5-22AB0DF572EB";
        public const  string TransactionColorProcessing = "47094F37-2AC4-4AE1-89A3-B65CF20334AA";
        public const  string TransactionColorReversed = "707D2895-50BA-42FC-9324-8AC648A033EA";
        public const  string KYC_IdentityServer_Act_As_UserLoginID = "420A69AA-9D92-45E1-919E-652FDBF68427";
        public const  string KYC_IdentityServer_API_URL = "40559C59-E5F5-4B5B-8143-BF52C5D9E4E7";
        public const  string KYC_IdentityServer_Is_CurrentServer_Master = "954B5C4C-3796-4543-A584-1EC46F7E930C";
        public const  string KYC_IdentityServer_Is_Enabled = "5E86AF4F-B9EC-46C2-8C57-F0A2B2F061A4";
        public const  string KYC_IdentityServer_SendTasksInterval = "BF6CB980-64BF-4E61-8979-6B944EA19FBE";
        public const  string KYC_IdentityServer_SignKey = "7A8745F8-04C8-45F8-AD16-614AC673AF4B";
        public const  string KYC_IdentityServer_Slave_ID = "CB61336A-CE5A-405E-81C8-B14D268C7134";
        public const  string ComplyAdvantageAPIKey = "1D913228-176A-4287-B75E-B5D909D50E98";
        public const  string ComplianceListCheck_NoCheckHours = "51EFB6D8-D2CA-44D1-9742-D5F1F18EFA40";
        public const  string NameScanAPIKey = "BA41AB6B-6002-407A-9863-1E30718B62BC";

        public const string SupportChatCategory = "9AB23B93-0434-4D66-8C84-152E16A0D688";

        public const string ConversionAllow  = "c32ba19d-6680-405f-90e4-c7d715d6a6d8";

        public const string IsAllowToDisableTwoStepAuthentication = "bed1bb74-902f-4f63-b030-e92788612223";


        public const string JWT_UseSetting = "182E4A3B-7948-41E9-86C9-E450A4D1639D";
        public const string JWT_TokenValidityIntervalSetting = "8336D863-4294-4840-906A-11E72B8DD8B1";

        public const string OriginatorEnabled = "A820E4EB-30A1-429E-8E76-68EEB51F7281";

        public const string ExtendedSubUsers = "440cb82b-98b5-4bca-ab7e-10df952e6d5e";

        public const string VeriffBackUrlSetting = "ecbfca44-e903-4f24-ba23-8f3c14e6e6ac";

        public const string AddUsersAbilityPersonal = "0CBE36DA-DE34-4892-9AE2-1962903B8B6A";
        public const string AddUsersAbilityCorporate = "E7D50521-8A2D-4540-8B28-C336EA67291E";

        public const string NBMURL = "3437C3F2-2BB1-421F-904A-E7C50CBEDA0B";
        public const string IsSingleCurrencyClientAccounts = "A9630B59-C0E0-44B4-B1E3-254172C46242";
        public const string IsOpenAccountAsProduct = "********-adcd-4048-9563-************";

        public const string MobileClientAppVersion = "82B5A057-159C-4055-BB2B-924E4EFC5CD7";

        public const string MobileClientAppStoreID = "6F0EDF80-B140-47BA-950D-ADDD79496FC4";


        public const string ServiceProviderDefaultChartOfAccount = "A55EC901-8576-49B5-A2E2-EF48FE8FE500";

        public const string PartnerAgentsChartOfAccountSetting = "3832155c-0f88-4e3d-92a8-af4e8e989ba2";

        public const string QoobissComponentUrl = "E1D8FD0E-E806-4D0F-B426-507C4E0B1E1E";
    }
}
