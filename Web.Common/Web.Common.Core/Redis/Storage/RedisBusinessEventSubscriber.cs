﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Web.Common.SignalR;

namespace Web.Common.Redis.Storage
{
    public class RedisBusinessEventSubscriber
    {
        public static void SubscribeOnEvents(IServiceProvider serviceProvider)
        {
            var _redisPubSubService = serviceProvider.GetService<RedisPubSubService>();
            var _redisCacheStore = serviceProvider.GetService<RedisCacheStore>();


            _redisPubSubService.SubscribeOnBusiness(SignalRConstants.BusinessNotificationsUpdateRedis, (channel, value) =>
            {                
                _redisCacheStore.Clear(value);
            });

        }
    }
}
