﻿namespace Web.Common.Redis.Storage
{
    using StackExchange.Redis;
    using System;
    using System.Collections.Generic;

    public interface IResourceStorage
    {
        RedisValue? GetResource(string key);

        List<string> GetResource(List<string> keys);

        IEnumerable<Dictionary<string, string>> GetAllResourceInCurrentCulture(string currentCulture);
        void SetResource(string key, string value, TimeSpan? expiry=null);
    }
}
