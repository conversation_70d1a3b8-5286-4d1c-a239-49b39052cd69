﻿namespace Web.Common.Redis.Storage
{
    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Configuration;
    using StackExchange.Redis;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public abstract class RedisResourceStorage : IResourceStorage
    {
        public const string USE_CACHE_ITEMS_KEY = "_UseCacheItems";


        private RedisWrapper _redisWrapper;
        private RedisCacheStore _cache;
        private IHttpContextAccessor _httpContextAccessor;
        private IConfiguration _configuration;


        public RedisResourceStorage()
        {

        }
        public void Init(RedisWrapper wrapper, RedisCacheStore cache, IHttpContextAccessor httpContextAccessor, IConfiguration configuration)
        {
            _redisWrapper = wrapper;
            _cache = cache;
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
        }

        
        protected virtual bool IsResourceStorageUseRedisCache => false;

        protected virtual int RedisDbNumber => 0;


        public IEnumerable<Dictionary<string, string>> GetAllResourceInCurrentCulture(string currentCulture)
        {

            throw new NotImplementedException();
        }

        public RedisValue? GetResource(string key)
        {
            var isUseCache = isUseCacheInRedis();

            if (isUseCache)
            {
                var isGet = _cache.TryGetValue(key, out var resource);
                if (isGet)
                {
                    return resource;
                }
            }

            var redisClient = GetRedisWrapper();
            var value = redisClient.GetString(RedisDbNumber, key);
            if (value == null)
            {
                return null;
            }
            if (value.Value.IsNull)
            {
                if (isUseCache && isUseCache)
                {
                    _cache.Add(key, null);
                }
                return null;
            }

            if (isUseCache && IsResourceStorageUseRedisCache)
            {
                _cache.Add(key, value.Value);
            }

            return value;
        }




        public List<string> GetResource(List<string> keys)
        {

            var _keys = keys.Select(key => (RedisKey)key).ToList();
            return GetResource(_keys);
        }

        public List<string> GetResource(List<RedisKey> keys)
        {
            var isUseCache = isUseCacheInRedis();
            if (isUseCache)
            {
                List<string> retval = new List<string>();

                foreach (var item in keys)
                {
                    var value = GetResource(item);
                    retval.Add(value);
                }
                return retval;
            }
            else
            {
                var redisClient = GetRedisWrapper();
                var value = redisClient.GetStrings(RedisDbNumber, keys);
                return value;
            }
        }

        public void DeleteResource(string key)
        {
            var redisClient = GetRedisWrapper();
            redisClient.DeleteKey(RedisDbNumber, key);
        }

        public void SetResource(string key, string value, TimeSpan? expiry = null)
        {
            var redisClient = GetRedisWrapper();
            redisClient.SetString(RedisDbNumber, key, value, expiry);
        }
        public void SetResource(string key, byte[] value, TimeSpan? expiry = null)
        {
            var redisClient = GetRedisWrapper();
            redisClient.SetString(RedisDbNumber, key, value, expiry);
        }


        public RedisWrapper GetRedisWrapper()
        {
            return _redisWrapper;
        }


        public IEnumerable<RedisKey> GetKeys(string key)
        {
            var server = _redisWrapper.GetServer();
            if (server != null)
            {
                var ff = server.Keys(RedisDbNumber, key, int.MaxValue);
                return ff;
            }
            return null;
        }

        private bool isUseCacheInRedis()
        {
            if (IsResourceStorageUseRedisCache == false)
            {
                return false;
            }            
            var isUseCacheInRedis = _configuration.GetValue<bool>("IsUseCacheInRedis", false);

            //use cache only in special case
            if (isUseCacheInRedis)
            {
                if (_httpContextAccessor != null)
                {
                    var isItem = _httpContextAccessor?.HttpContext?.Items[USE_CACHE_ITEMS_KEY] != null;
                    if (isItem)
                    {

                    }

                    return isItem;
                }
            }

            return false;
        }

    }

    public class CurrencyRatesRedisResourceStorage : RedisResourceStorage
    {

    }

    public class SettingsRedisResourceStorage : RedisResourceStorage
    {
        protected override bool IsResourceStorageUseRedisCache => true;
    }

    public class TranslationRedisResourceStorage : RedisResourceStorage
    {
        protected override bool IsResourceStorageUseRedisCache => true;
    }

    public class ContentRedisResourceStorage : RedisResourceStorage
    {

    }
  
    public class CacheRedisResourceStorage : RedisResourceStorage
    {      
    }

    public class KYCRequirementsResourceStorage : RedisResourceStorage
    {

    }


}
