﻿using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Web.Common.Redis
{
    internal static class RedisExtensions
    {
        private const string HmGetScript = (@"return redis.call('HMGET', KEYS[1], unpack(ARGV))");

        internal static RedisValue[] HashMemberGet(this IDatabase cache, string key, params string[] members)
        {
            int wait = 10;
            int retryCount = 3;

            int i = 0;
            do
            {
                try
                {
                    var result = cache.ScriptEvaluate(
                HmGetScript,
                new RedisKey[] { key },
                GetRedisMembers(members));

                    // TODO: Error checking?
                    return (RedisValue[])result;
                }
                catch (Exception)
                {
                    if (i < retryCount + 1)
                    {
                        System.Threading.Thread.Sleep(wait);
                        i++;
                    }
                    else throw;
                }
            }
            while (i < retryCount + 1);
            return null;

        }

        internal static async Task<RedisValue[]> HashMemberGetAsync(
            this IDatabase cache,
            string key,
            params string[] members)
        {

            int wait = 10;
            int retryCount = 3;

            int i = 0;
            do
            {
                try
                {
                    var result = await cache.ScriptEvaluateAsync(
                        HmGetScript,
                        new RedisKey[] { key },
                        GetRedisMembers(members));

                    // TODO: Error checking?
                    return (RedisValue[])result;
                }
                catch (Exception)
                {
                    if (i < retryCount + 1)
                    {
                        System.Threading.Thread.Sleep(wait);
                        i++;
                    }
                    else throw;
                }
            }
            while (i < retryCount + 1);
            return null;
        }

        private static RedisValue[] GetRedisMembers(params string[] members)
        {
            var redisMembers = new RedisValue[members.Length];
            for (int i = 0; i < members.Length; i++)
            {
                redisMembers[i] = (RedisValue)members[i];
            }

            return redisMembers;
        }
    }
}
