﻿
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading;
//using System.Threading.Tasks;
//using Microsoft.Extensions.Caching.Distributed;
//using StackExchange.Redis;


//namespace Web.Common.Redis
//{
//    public class RedisSessionCache : IDistributedCache, IDisposable
//    {
//        // KEYS[1] = = key
//        // ARGV[1] = absolute-expiration - ticks as long (-1 for none)
//        // ARGV[2] = sliding-expiration - ticks as long (-1 for none)
//        // ARGV[3] = relative-expiration (long, in seconds, -1 for none) - Min(absolute-expiration - Now, sliding-expiration)
//        // ARGV[4] = data - byte[]
//        // this order should not change LUA script depends on it
//        private const string SetScript = (@"
//                redis.call('HMSET', KEYS[1], 'absexp', ARGV[1], 'sldexp', ARGV[2], 'data', ARGV[4])
//                if ARGV[3] ~= '-1' then
//                  redis.call('EXPIRE', KEYS[1], ARGV[3])
//                end
//                return 1");


//        private const string AbsoluteExpirationKey = "absexp";
//        private const string SlidingExpirationKey = "sldexp";
//        private const string DataKey = "data";
//        private const long NotPresent = -1;

//        private IConnectionMultiplexer _redisMultiplexer;
//        private readonly ConnectionMultiplexerCreator _redisMultiplexerFactory;
//        private readonly RedisWrapper _redisWrapper;

//        //private volatile ConnectionMultiplexer _connection;
//        private IDatabase _cache;

//        //private readonly RedisCacheOptions _options;
//        private readonly string _instance;
//        private readonly int _dbNumber;


//        private readonly SemaphoreSlim _connectionLock = new SemaphoreSlim(initialCount: 1, maxCount: 1);

//        public RedisSessionCache(ConnectionMultiplexerCreator connectionMultiplexerFactory, int dbNumber, string instanceName)
//        {
//            // This allows partitioning a single backend cache for use with multiple apps/services.
//            _dbNumber = dbNumber;
//            _instance = instanceName ?? string.Empty;
//            _redisMultiplexerFactory = connectionMultiplexerFactory;
//        }

//        public RedisSessionCache(RedisWrapper redisWrapper, int dbNumber, string instanceName)
//        {
//            _dbNumber = dbNumber;
//            _instance = instanceName ?? string.Empty;
//            _redisWrapper = redisWrapper;
//        }


//        public byte[] Get(string key)
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            return GetAndRefresh(key, getData: true);
//        }

//        public async Task<byte[]> GetAsync(string key, CancellationToken token = default(CancellationToken))
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            token.ThrowIfCancellationRequested();

//            return await GetAndRefreshAsync(key, getData: true, token: token);
//        }

//        public void Set(string key, byte[] value, DistributedCacheEntryOptions options)
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            if (value == null)
//            {
//                throw new ArgumentNullException(nameof(value));
//            }

//            if (options == null)
//            {
//                throw new ArgumentNullException(nameof(options));
//            }

//            Connect();

//            var creationTime = DateTimeOffset.Now;

//            var absoluteExpiration = GetAbsoluteExpiration(creationTime, options);

//            var result = _cache.ScriptEvaluate(SetScript, new RedisKey[] { _instance + key },
//                new RedisValue[]
//                {
//                        absoluteExpiration?.Ticks ?? NotPresent,
//                        options.SlidingExpiration?.Ticks ?? NotPresent,
//                        GetExpirationInSeconds(creationTime, absoluteExpiration, options) ?? NotPresent,
//                        value
//                });
//        }

//        public async Task SetAsync(string key, byte[] value, DistributedCacheEntryOptions options, CancellationToken token = default(CancellationToken))
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            if (value == null)
//            {
//                throw new ArgumentNullException(nameof(value));
//            }

//            if (options == null)
//            {
//                throw new ArgumentNullException(nameof(options));
//            }

//            token.ThrowIfCancellationRequested();

//            await ConnectAsync(token);

//            var creationTime = DateTimeOffset.Now;

//            var absoluteExpiration = GetAbsoluteExpiration(creationTime, options);



//            await _cache.ScriptEvaluateAsync(SetScript, new RedisKey[] { _instance + key },
//                new RedisValue[]
//                {
//                        absoluteExpiration?.Ticks ?? NotPresent,
//                        options.SlidingExpiration?.Ticks ?? NotPresent,
//                        GetExpirationInSeconds(creationTime, absoluteExpiration, options) ?? NotPresent,
//                        value
//                });
//        }

//        public void Refresh(string key)
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            GetAndRefresh(key, getData: false);
//        }

//        public async Task RefreshAsync(string key, CancellationToken token = default(CancellationToken))
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            token.ThrowIfCancellationRequested();

//            await GetAndRefreshAsync(key, getData: false, token: token);
//        }

//        private void Connect()
//        {
//            if (_cache != null)
//            {
//                return;
//            }

//            _connectionLock.Wait();
//            try
//            {
//                if (_cache == null)
//                {
//                    if (_redisWrapper != null)
//                    {
//                        _redisWrapper.InitMultiplexer();
//                        _cache = _redisWrapper.GetDatabase(_dbNumber);
//                    }
//                    else
//                    {
//                        _redisMultiplexer = _redisMultiplexerFactory.Create();
//                        _redisMultiplexer.PreserveAsyncOrder = false;
//                        _cache = _redisMultiplexer.GetDatabase(_dbNumber);
//                    }
//                }
//            }
//            finally
//            {
//                _connectionLock.Release();
//            }
//        }

//        private async Task ConnectAsync(CancellationToken token = default(CancellationToken))
//        {
//            token.ThrowIfCancellationRequested();

//            if (_cache != null)
//            {
//                return;
//            }



//            await _connectionLock.WaitAsync(token);
//            try
//            {
//                if (_cache == null)
//                {
//                    if (_redisWrapper != null)
//                    {
//                        _redisWrapper.InitMultiplexer();
//                        _cache = _redisWrapper.GetDatabase(_dbNumber);
//                    }
//                    else
//                    {
//                        _redisMultiplexer = await _redisMultiplexerFactory.CreateAsync();
//                        _redisMultiplexer.PreserveAsyncOrder = false;
//                        _cache = _redisMultiplexer.GetDatabase(_dbNumber);
//                    }
//                }
//            }
//            finally
//            {
//                _connectionLock.Release();
//            }
//        }

//        private byte[] GetAndRefresh(string key, bool getData)
//        {

//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            Connect();

//            // This also resets the LRU status as desired.
//            // TODO: Can this be done in one operation on the server side? Probably, the trick would just be the DateTimeOffset math.
//            RedisValue[] results;
//            if (getData)
//            {
//                results = _cache.HashMemberGet(_instance + key, AbsoluteExpirationKey, SlidingExpirationKey, DataKey);
//            }
//            else
//            {
//                results = _cache.HashMemberGet(_instance + key, AbsoluteExpirationKey, SlidingExpirationKey);
//            }

//            // TODO: Error handling
//            if (results.Length >= 2)
//            {
//                MapMetadata(results, out DateTimeOffset? absExpr, out TimeSpan? sldExpr);
//                Refresh(key, absExpr, sldExpr);
//            }

//            if (results.Length >= 3 && results[2].HasValue)
//            {
//                return results[2];
//            }

//            return null;
//        }

//        private async Task<byte[]> GetAndRefreshAsync(string key, bool getData, CancellationToken token = default(CancellationToken))
//        {


//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            token.ThrowIfCancellationRequested();

//            await ConnectAsync(token);


//            // This also resets the LRU status as desired.
//            // TODO: Can this be done in one operation on the server side? Probably, the trick would just be the DateTimeOffset math.
//            RedisValue[] results;
//            if (getData)
//            {
//                results = await _cache.HashMemberGetAsync(_instance + key, AbsoluteExpirationKey, SlidingExpirationKey, DataKey);
//            }
//            else
//            {
//                results = await _cache.HashMemberGetAsync(_instance + key, AbsoluteExpirationKey, SlidingExpirationKey);
//            }

//            // TODO: Error handling
//            if (results.Length >= 2)
//            {
//                MapMetadata(results, out DateTimeOffset? absExpr, out TimeSpan? sldExpr);
//                await RefreshAsync(key, absExpr, sldExpr, token);
//            }

//            if (results.Length >= 3 && results[2].HasValue)
//            {
//                return results[2];
//            }

//            return null;
//        }

//        public void Remove(string key)
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            Connect();

//            _cache.KeyDelete(_instance + key);
//            // TODO: Error handling
//        }

//        public async Task RemoveAsync(string key, CancellationToken token = default(CancellationToken))
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            await ConnectAsync(token);

//            await _cache.KeyDeleteAsync(_instance + key);
//            // TODO: Error handling
//        }

//        private void MapMetadata(RedisValue[] results, out DateTimeOffset? absoluteExpiration, out TimeSpan? slidingExpiration)
//        {
//            absoluteExpiration = null;
//            slidingExpiration = null;
//            var absoluteExpirationTicks = (long?)results[0];
//            if (absoluteExpirationTicks.HasValue && absoluteExpirationTicks.Value != NotPresent)
//            {
//                absoluteExpiration = new DateTimeOffset(absoluteExpirationTicks.Value, TimeSpan.Zero);
//            }
//            var slidingExpirationTicks = (long?)results[1];
//            if (slidingExpirationTicks.HasValue && slidingExpirationTicks.Value != NotPresent)
//            {
//                slidingExpiration = new TimeSpan(slidingExpirationTicks.Value);
//            }
//        }

//        private void Refresh(string key, DateTimeOffset? absExpr, TimeSpan? sldExpr)
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            // Note Refresh has no effect if there is just an absolute expiration (or neither).
//            TimeSpan? expr = null;
//            if (sldExpr.HasValue)
//            {
//                if (absExpr.HasValue)
//                {
//                    var relExpr = absExpr.Value - DateTimeOffset.Now;
//                    expr = relExpr <= sldExpr.Value ? relExpr : sldExpr;
//                }
//                else
//                {
//                    expr = sldExpr;
//                }
//                _cache.KeyExpire(_instance + key, expr);
//                // TODO: Error handling
//            }
//        }

//        private async Task RefreshAsync(string key, DateTimeOffset? absExpr, TimeSpan? sldExpr, CancellationToken token = default(CancellationToken))
//        {
//            if (key == null)
//            {
//                throw new ArgumentNullException(nameof(key));
//            }

//            token.ThrowIfCancellationRequested();

//            // Note Refresh has no effect if there is just an absolute expiration (or neither).
//            TimeSpan? expr = null;
//            if (sldExpr.HasValue)
//            {
//                if (absExpr.HasValue)
//                {
//                    var relExpr = absExpr.Value - DateTimeOffset.Now;
//                    expr = relExpr <= sldExpr.Value ? relExpr : sldExpr;
//                }
//                else
//                {
//                    expr = sldExpr;
//                }
//                await _cache.KeyExpireAsync(_instance + key, expr);
//                // TODO: Error handling
//            }
//        }

//        private static long? GetExpirationInSeconds(DateTimeOffset creationTime, DateTimeOffset? absoluteExpiration, DistributedCacheEntryOptions options)
//        {
//            if (absoluteExpiration.HasValue && options.SlidingExpiration.HasValue)
//            {
//                return (long)Math.Min(
//                    (absoluteExpiration.Value - creationTime).TotalSeconds,
//                    options.SlidingExpiration.Value.TotalSeconds);
//            }
//            else if (absoluteExpiration.HasValue)
//            {
//                return (long)(absoluteExpiration.Value - creationTime).TotalSeconds;
//            }
//            else if (options.SlidingExpiration.HasValue)
//            {
//                return (long)options.SlidingExpiration.Value.TotalSeconds;
//            }
//            return null;
//        }

//        private static DateTimeOffset? GetAbsoluteExpiration(DateTimeOffset creationTime, DistributedCacheEntryOptions options)
//        {
//            if (options.AbsoluteExpiration.HasValue && options.AbsoluteExpiration <= creationTime)
//            {
//                throw new ArgumentOutOfRangeException(
//                    nameof(DistributedCacheEntryOptions.AbsoluteExpiration),
//                    options.AbsoluteExpiration.Value,
//                    "The absolute expiration value must be in the future.");
//            }
//            var absoluteExpiration = options.AbsoluteExpiration;
//            if (options.AbsoluteExpirationRelativeToNow.HasValue)
//            {
//                absoluteExpiration = creationTime + options.AbsoluteExpirationRelativeToNow;
//            }

//            return absoluteExpiration;
//        }


//        public void Dispose()
//        {
//            if (_redisMultiplexer != null)
//            {
//                _redisMultiplexer.Close();
//            }
//        }
//    }




//}
