﻿using StackExchange.Redis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Web.Common.Redis
{
    public class RedisCacheStore
    {
      
        private static ConcurrentDictionary<string, string> _data = new ConcurrentDictionary<string, string>();

        public bool TryGetValue(string key, out string value)
        {
            return _data.TryGetValue(key, out value);
        }

        public bool TryGetValues(List<string> keys, out List<string> values)
        {
            values = new List<string>();
            var retval = true;

            foreach (var key in keys)
            {
                if (_data.TryGetValue(key, out string value))
                {
                    values.Add(value);
                }
                else
                {
                    retval = false;
                }
            }
            return retval;          
        }

        public void Add(string key, string value)
        {
            _data[key] = value;
        }
                        


        public void Clear(string pattern)
        {
            _data.Clear();
        }


    }
}
