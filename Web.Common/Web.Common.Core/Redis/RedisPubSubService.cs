﻿using System;
using System.Collections.Generic;
using System.Text;
using Web.Common.Redis.Storage;

namespace Web.Common.Redis
{
    public class RedisPubSubService
    {
        public RedisPubSubService(RedisWrapper backFrontSharedRedisWrapper, RedisWrapper businessRedisWrapper)
        {
            _businessRedisWrapper = businessRedisWrapper;
            _backFrontSharedRedisWrapper = backFrontSharedRedisWrapper;
        }
        private readonly RedisWrapper _backFrontSharedRedisWrapper;
        private readonly RedisWrapper _businessRedisWrapper;


        public void Publish(string channel, string message)
        {
            _backFrontSharedRedisWrapper.Publish(channel, message);
        }

        public void Subscribe(string channel, Action<string, string> handler)
        {
            _backFrontSharedRedisWrapper.Subscribe(channel, handler);
        }


        public void SubscribeOnBusiness(string channel, Action<string, string> handler)
        {
            _businessRedisWrapper.Subscribe(channel, handler);
        }

        public string GetFromBusiness(string path)
        {
            return _businessRedisWrapper.GetString(0, path)?.ToString();
        }
    }
}
