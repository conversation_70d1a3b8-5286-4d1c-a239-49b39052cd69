﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.SignalR
{
    public class AccountSignalRModel
    {
        public AccountSignalRModel(int clientID)
        {
            this.ClientID = clientID;
        }

        public int ClientID { get; set; }
    }

    public class SupportSignalRModel : AccountSignalRModel
    {
        public SupportSignalRModel(int clientID, int ticketID, short categoryID) : base(clientID)
        {
            TicketID = ticketID;
            CategoryID = categoryID;

        }

        public int TicketID { get; set; }
        public short CategoryID { get; set; }
    }


    public class TypingClientModel : SupportSignalRModel
    {
        public TypingClientModel(int clientID, int ticketID, short categoryID) : base(clientID, ticketID, categoryID)
        {

        }
    }

    public class TypingManagerModel : SupportSignalRModel
    {
        public string HumanReadableID { get; set; }
        public TypingManagerModel(int clientID, int ticketID, short categoryID, string humanReadableID) : base(clientID, ticketID, categoryID)
        {
            this.HumanReadableID = humanReadableID;
        }
    }


    public class NewMessageClientModel : SupportSignalRModel
    {

        public NewMessageClientModel(int clientID, int ticketID, short categoryID, bool isNewTicket) : base(clientID, ticketID, categoryID)
        {
            IsNewTicket = isNewTicket;
        }

        public bool IsNewTicket { get; set; }
    }



    public class NewMessageManagerModel : SupportSignalRModel
    {
        public string ReadableTicketID { get; set; }
        public NewMessageManagerModel(int clientID, int ticketID, short categoryID, string readableTicketID) : base(clientID, ticketID, categoryID)
        {
            ReadableTicketID = readableTicketID;
        }
    }


    public class DeleteMessageManagerModel : SupportSignalRModel
    {
        public int MessageID { get; set; }
        public DeleteMessageManagerModel(int clientID, int ticketID, int messageID) : base(clientID, ticketID, 0)
        {
            this.MessageID = messageID;
        }
    }


    public class EditMessageManagerModel : SupportSignalRModel
    {
        public int MessageID { get; set; }
        public string NewText { get; set; }
        public EditMessageManagerModel(int clientID, int ticketID, int messageID, string newText) : base(clientID, ticketID, 0)
        {
            this.MessageID = messageID;
            this.NewText = newText;
        }
    }



    public class ClientOpenTicketModel : SupportSignalRModel
    {
        public ClientOpenTicketModel(int clientID, int ticketID, short categoryID) : base(clientID, ticketID, categoryID)
        {

        }
    }

    public class CloseTicketClientModel : SupportSignalRModel
    {
        public CloseTicketClientModel(int clientID, int ticketID, short categoryID) : base(clientID, ticketID, categoryID)
        {

        }
    }

}
