﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Web.Common.SignalR
{
    public static class SignalRConstants
    {
        public const string AccountEventsChanel = "AccountEventsChanel";
        public const string BusinessNotificationsUpdatedChanel = "FrontofficeWebSiteNotification:UserNotificationsUpdated";

        public const string BusinessNotificationsTransactionStatusChangedChanel = "FrontofficeWebSiteNotification:TransactionStatusChanged";

        public const string BusinessNotificationsKYCLevelChangedChanel = "FrontofficeWebSiteNotification:KYCLevelChanged";

        public const string BusinessNotificationsVideoVerificationUpdatedChanel = "BackofficeNotification:VideoVerificationUpdated";        
        public const string BusinessNotificationsVideoVerificationManagerStatusChanel = "BackofficeNotification:VideoVerificationManagerStatus";

        public const string BusinessNotificationsProductCasesAmount = "BackofficeNotification:ProductCasesCount";

        


        

        public const string BusinessNotificationsUpdateRedis = "Notification:UpdateRedis";


        public const string AwaitingFRQsCount = "BackofficeNotification:AwaitingFRQsCount";


        public const string TypingManager = "SupportEventsTypingManager";
        public const string TypingClient = "SupportEventsTypingClient";

        public const string NewMessageManager = "SupportEventsNewMessageManager";
        public const string NewMessageClient = "SupportEventsNewMessageClient";

        public const string ClientOpenTicket = "SupportEventsClientOpenTicket";
        public const string ClientCloseTicket = "SupportEventsClientCloseTicket";



        public const string DeleteMessageManager = "SupportEventsDeleteMessageManager";
        public const string EditMessageManager = "SupportEventsEditMessageManager";

        
        public const string BusinessNotificationsRTPReceivedChanel = "FrontofficeWebSiteNotification:RTPReceived";
        public const string BusinessNotificationsRTPStatusChangedChanel = "FrontofficeWebSiteNotification:RTPStatusChanged";

        public const string BusinessNotificationsTwilioVerifyWebhookChanel = "FrontofficeWebSiteNotification:TwilioVerifyWebhook";



        public const string RedisEvents = "__keyspace@0__:*";

        public const string RedisEventsOrdersChanel = "order";
        public const string RedisEventsOrdersUdpate = "zadd";

        public const string RedisEventsCandleChanel = "interval:60";
        public const string RedisEventsCandleUdpate = "zadd";//"lpush";


    }
}
