﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Web.Common</AssemblyName>
    <RootNamespace>Web.Common</RootNamespace>
    <ApplicationIcon />
    <StartupObject />
    <Configurations>Debug;Release;Test</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Proto\rpcmessage.proto" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWS.Logger.NLog" Version="1.5.2" />
    <PackageReference Include="Google.Protobuf" Version="3.17.3" />
    <PackageReference Include="Grpc.Net.Client" Version="2.38.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis" Version="8.0.3" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.3" />    
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="NLog" Version="4.5.2" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.5.2" />
    <PackageReference Include="Quartz" Version="3.0.7" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.33" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Proto\rpcmessage.proto">
      <GrpcServices>Client</GrpcServices>
    </Protobuf>
  </ItemGroup>

</Project>
