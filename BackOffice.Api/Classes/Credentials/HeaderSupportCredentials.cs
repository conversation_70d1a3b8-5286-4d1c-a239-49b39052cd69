using Backoffice.Classes.Infrastructure;
using Backoffice.Credentials;
using CloudLink.IEngineWraperBackOfficeProxy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Web.Common.Support.Proxy;

namespace BackOffice.Api.Classes.Credentials
{
    public class HeaderSupportCredentials : ISupportCredentials
    {
        public HeaderSupportCredentials(IProxyFactory requestEngineFactory, IEngineCredentials engineCredentials,
          IRequestEngine requestEngine, IRequestSupportFactoryAnonymous supportFactory)
        {
            _requestEngineFactory = requestEngineFactory;
            _engineCredentials = engineCredentials;
            _requestEngine = requestEngine;
            _supportFactory = supportFactory;
        }

        private readonly IProxyFactory _requestEngineFactory;
        private readonly IEngineCredentials _engineCredentials;
        private readonly IRequestEngine _requestEngine;
        IRequestSupportFactoryAnonymous _supportFactory;


        public Web.Common.Support.Proxy.EngineCredentialsModel Credentials()
        {
            try
            {
                var cred = _engineCredentials.Credentials();                
                using (var proxy = _requestEngineFactory.GetProxyEngineAuthorized())
                {
                    using (var proxySupport = _supportFactory.ProxyAnonymous())
                    {
                        var token = proxy.SSO_GetToken_Self();
                        var sso = proxySupport.User_Login_SSO(cred.userID, token);

                        var retval = new Web.Common.Support.Proxy.EngineCredentialsModel
                        {
                            sessionID = sso.SessionID,
                            sessionPassword = sso.SessionPassword,
                            userID = sso.UserID
                        };
                        return retval;
                    }

                }

            }
            catch (Exception ex)
            {
                throw new Exception(Errors.Access_Denied.ToString(), ex);
            }

        }
    }
}
