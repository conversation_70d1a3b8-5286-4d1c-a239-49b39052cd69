<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>a0a08cca-849f-4549-826e-6cc3393ca786</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <AssemblyName>BackOffice.Api</AssemblyName>
    <RootNamespace>BackOffice.Api</RootNamespace>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>..\BackOffice.Api.Mobile\BackOffice.Api.Mobile.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\MobileApiControllerClientFiatBeneficiary.cs" />
    <Compile Remove="Controllers\MobileApiControllerClientLookup.cs" />
    <Compile Remove="Controllers\MobileApiControllerFiat.cs" />
    <Compile Remove="Controllers\MobileApiControllerFiatTransfer.cs" />
    <Compile Remove="Controllers\MobileApiControllerGeneral.cs" />
    <Compile Remove="Controllers\MobileApiControllerNotifications.cs" />
    <Compile Remove="Controllers\MobileApiControllerResetPassword.cs" />
    <Compile Remove="Controllers\MobileApiControllerSettings.cs" />
    <Compile Remove="Controllers\MobileApiControllerSupport.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="9.0.0" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.9.10" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="QRCoder" Version="1.3.6" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />

    <PackageReference Include="FluentScheduler" Version="5.5.1" />
    <PackageReference Include="DalSoft.Hosting.BackgroundQueue" Version="1.0.4" />
    <PackageReference Include="Google.Protobuf" Version="3.27.2" />
    <PackageReference Include="Grpc.Net.Client" Version="2.65.0" />
    <PackageReference Include="Grpc.Tools" Version="2.60.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>

    <PackageReference Include="Joonasw.AspNetCore.SecurityHeaders" Version="5.0.0" />

    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.3" />

    <PackageReference Include="ZXing.Net" Version="0.16.5" />

    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="8.0.3" />
    
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BackOffice.Common\BackOffice.Common.csproj" />
    <ProjectReference Include="..\Web.Common\Web.Common.Core.SupportLink\Web.Common.SupportLink.csproj" />
    <ProjectReference Include="..\Web.Common\Web.Common.Core.Support\Web.Common.Support.csproj" />
    <ProjectReference Include="..\Web.Common\Web.Common.Core\Web.Common.csproj" />
    <ProjectReference Include="..\Backoffice\Backoffice.csproj" />
    <ProjectReference Include="..\Backoffice.Proxy\Backoffice.Proxy.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Properties\PublishProfiles\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="BackOffice.Api.Documentation.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="files\GeoLite2-City.mmdb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>


</Project>
