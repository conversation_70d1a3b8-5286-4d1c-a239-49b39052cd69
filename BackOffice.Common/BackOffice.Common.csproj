<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />

		<PackageReference Include="DalSoft.Hosting.BackgroundQueue" Version="1.0.4" />
		<PackageReference Include="FluentScheduler" Version="5.5.1" />
		<PackageReference Include="Google.Protobuf" Version="3.17.3" />
		<PackageReference Include="Grpc.Net.Client" Version="2.38.0" />
		<PackageReference Include="Grpc.Tools" Version="2.60.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Joonasw.AspNetCore.SecurityHeaders" Version="5.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.ViewFeatures" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="8.0.3" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.9.10" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.2" />
		<PackageReference Include="QRCoder" Version="1.3.6" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.3" />		
		<PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
		<PackageReference Include="WebMarkupMin.AspNetCore2" Version="2.7.0" />
		<PackageReference Include="ZXing.Net" Version="0.16.5" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />
		<PackageReference Include="Net.Codecrete.QrCodeGenerator" Version="2.0.3" />
		<PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.2" />
		<PackageReference Include="Mime" Version="3.6.0" />
		<PackageReference Include="MaxMind.GeoIP2" Version="5.2.0" />
		

	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Web.Common\Web.Common.Core\Web.Common.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Protobuf Include="Protos\business.proto" GrpcServices="Client" />
	</ItemGroup>

</Project>
